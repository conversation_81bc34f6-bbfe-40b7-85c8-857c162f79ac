<div class="flex flex-wrap gap-4 sm:items-center sm:justify-center">
  <!-- Primary Badge-->
  <span
    class="inline-flex items-center justify-center gap-1 rounded-full bg-brand-500 px-2.5 py-0.5 text-sm font-medium text-white"
  >
    Primary
  </span>

  <!-- Success Badge-->
  <span
    class="inline-flex items-center justify-center gap-1 rounded-full bg-success-500 px-2.5 py-0.5 text-sm font-medium text-white"
  >
    Success
  </span>

  <!-- Error <PERSON>ge-->
  <span
    class="inline-flex items-center justify-center gap-1 rounded-full bg-error-500 px-2.5 py-0.5 text-sm font-medium text-white"
  >
    Error
  </span>

  <!-- Warning Badge-->
  <span
    class="inline-flex items-center justify-center gap-1 rounded-full bg-warning-500 px-2.5 py-0.5 text-sm font-medium text-white"
  >
    Warning
  </span>

  <!-- Info Badge-->
  <span
    class="inline-flex items-center justify-center gap-1 rounded-full bg-blue-light-500 px-2.5 py-0.5 text-sm font-medium text-white"
  >
    Info
  </span>

  <!-- Light Badge-->
  <span
    class="inline-flex items-center justify-center gap-1 rounded-full bg-gray-400 px-2.5 py-0.5 text-sm font-medium text-white dark:bg-white/5 dark:text-white/80"
  >
    Light
  </span>

  <!-- Dark Badge-->
  <span
    class="inline-flex items-center justify-center gap-1 rounded-full bg-gray-800 px-2.5 py-0.5 text-sm font-medium text-white dark:bg-white/15 dark:text-white"
  >
    Dark
  </span>
</div>
