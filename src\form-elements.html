<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
    />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>
      Form Elements | TailAdmin - Tailwind CSS Admin Dashboard Template
    </title>
  </head>
  <body
    x-data="{ page: 'formElements', 'loaded': true, 'darkMode': false, 'stickyMenu': false, 'sidebarToggle': false, 'scrollTop': false }"
    x-init="
         darkMode = JSON.parse(localStorage.getItem('darkMode'));
         $watch('darkMode', value => localStorage.setItem('darkMode', JSON.stringify(value)))"
    :class="{'dark bg-gray-900': darkMode === true}"
  >
    <!-- ===== Preloader Start ===== -->
    <include src="./partials/preloader.html"></include>
    <!-- ===== Preloader End ===== -->

    <!-- ===== Page Wrapper Start ===== -->
    <div class="flex h-screen overflow-hidden">
      <!-- ===== Sidebar Start ===== -->
      <include src="./partials/sidebar.html"></include>
      <!-- ===== Sidebar End ===== -->

      <!-- ===== Content Area Start ===== -->
      <div
        class="relative flex flex-1 flex-col overflow-x-hidden overflow-y-auto"
      >
        <!-- Small Device Overlay Start -->
        <include src="./partials/overlay.html" />
        <!-- Small Device Overlay End -->

        <!-- ===== Header Start ===== -->
        <include src="./partials/header.html" />
        <!-- ===== Header End ===== -->

        <!-- ===== Main Content Start ===== -->
        <main>
          <div class="mx-auto max-w-(--breakpoint-2xl) p-4 md:p-6">
            <!-- Breadcrumb Start -->
            <div x-data="{ pageName: `Form Elements`}">
              <include src="./partials/breadcrumb.html" />
            </div>
            <!-- Breadcrumb End -->

            <!-- ====== Form Elements Section Start -->
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <div class="space-y-6">
                <div
                  class="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]"
                >
                  <div class="px-5 py-4 sm:px-6 sm:py-5">
                    <h3
                      class="text-base font-medium text-gray-800 dark:text-white/90"
                    >
                      Default Inputs
                    </h3>
                  </div>
                  <div
                    class="space-y-6 border-t border-gray-100 p-5 sm:p-6 dark:border-gray-800"
                  >
                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Input
                      </label>
                      <input
                        type="text"
                        class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                      />
                    </div>

                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Input with Placeholder
                      </label>
                      <input
                        type="text"
                        placeholder="<EMAIL>"
                        class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                      />
                    </div>

                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Select Input
                      </label>
                      <div
                        x-data="{ isOptionSelected: false }"
                        class="relative z-20 bg-transparent"
                      >
                        <select
                          class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full appearance-none rounded-lg border border-gray-300 bg-transparent bg-none px-4 py-2.5 pr-11 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                          :class="isOptionSelected && 'text-gray-800 dark:text-white/90'"
                          @change="isOptionSelected = true"
                        >
                          <option
                            value=""
                            class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                          >
                            Select Option
                          </option>
                          <option
                            value=""
                            class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                          >
                            Marketing
                          </option>
                          <option
                            value=""
                            class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                          >
                            Template
                          </option>
                          <option
                            value=""
                            class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                          >
                            Development
                          </option>
                        </select>
                        <span
                          class="pointer-events-none absolute top-1/2 right-4 z-30 -translate-y-1/2 text-gray-500 dark:text-gray-400"
                        >
                          <svg
                            class="stroke-current"
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M4.79175 7.396L10.0001 12.6043L15.2084 7.396"
                              stroke=""
                              stroke-width="1.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                        </span>
                      </div>
                    </div>

                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Password Input
                      </label>
                      <div x-data="{ showPassword: false }" class="relative">
                        <input
                          :type="showPassword ? 'text' : 'password'"
                          placeholder="Enter your password"
                          class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full appearance-none rounded-lg border border-gray-300 bg-transparent bg-none px-4 py-2.5 pr-11 pl-4 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                        />
                        <span
                          @click="showPassword = !showPassword"
                          class="absolute top-1/2 right-4 z-30 -translate-y-1/2 cursor-pointer"
                        >
                          <svg
                            x-show="!showPassword"
                            class="fill-gray-500 dark:fill-gray-400"
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M10.0002 13.8619C7.23361 13.8619 4.86803 12.1372 3.92328 9.70241C4.86804 7.26761 7.23361 5.54297 10.0002 5.54297C12.7667 5.54297 15.1323 7.26762 16.0771 9.70243C15.1323 12.1372 12.7667 13.8619 10.0002 13.8619ZM10.0002 4.04297C6.48191 4.04297 3.49489 6.30917 2.4155 9.4593C2.3615 9.61687 2.3615 9.78794 2.41549 9.94552C3.49488 13.0957 6.48191 15.3619 10.0002 15.3619C13.5184 15.3619 16.5055 13.0957 17.5849 9.94555C17.6389 9.78797 17.6389 9.6169 17.5849 9.45932C16.5055 6.30919 13.5184 4.04297 10.0002 4.04297ZM9.99151 7.84413C8.96527 7.84413 8.13333 8.67606 8.13333 9.70231C8.13333 10.7286 8.96527 11.5605 9.99151 11.5605H10.0064C11.0326 11.5605 11.8646 10.7286 11.8646 9.70231C11.8646 8.67606 11.0326 7.84413 10.0064 7.84413H9.99151Z"
                            />
                          </svg>

                          <svg
                            x-show="showPassword"
                            class="fill-gray-500 dark:fill-gray-400"
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M4.63803 3.57709C4.34513 3.2842 3.87026 3.2842 3.57737 3.57709C3.28447 3.86999 3.28447 4.34486 3.57737 4.63775L4.85323 5.91362C3.74609 6.84199 2.89363 8.06395 2.4155 9.45936C2.3615 9.61694 2.3615 9.78801 2.41549 9.94558C3.49488 13.0957 6.48191 15.3619 10.0002 15.3619C11.255 15.3619 12.4422 15.0737 13.4994 14.5598L15.3625 16.4229C15.6554 16.7158 16.1302 16.7158 16.4231 16.4229C16.716 16.13 16.716 15.6551 16.4231 15.3622L4.63803 3.57709ZM12.3608 13.4212L10.4475 11.5079C10.3061 11.5423 10.1584 11.5606 10.0064 11.5606H9.99151C8.96527 11.5606 8.13333 10.7286 8.13333 9.70237C8.13333 9.5461 8.15262 9.39434 8.18895 9.24933L5.91885 6.97923C5.03505 7.69015 4.34057 8.62704 3.92328 9.70247C4.86803 12.1373 7.23361 13.8619 10.0002 13.8619C10.8326 13.8619 11.6287 13.7058 12.3608 13.4212ZM16.0771 9.70249C15.7843 10.4569 15.3552 11.1432 14.8199 11.7311L15.8813 12.7925C16.6329 11.9813 17.2187 11.0143 17.5849 9.94561C17.6389 9.78803 17.6389 9.61696 17.5849 9.45938C16.5055 6.30925 13.5184 4.04303 10.0002 4.04303C9.13525 4.04303 8.30244 4.17999 7.52218 4.43338L8.75139 5.66259C9.1556 5.58413 9.57311 5.54303 10.0002 5.54303C12.7667 5.54303 15.1323 7.26768 16.0771 9.70249Z"
                            />
                          </svg>
                        </span>
                      </div>
                    </div>

                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Date Picker Input
                      </label>

                      <div class="relative">
                        <input
                          type="date"
                          placeholder="Select date"
                          class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full appearance-none rounded-lg border border-gray-300 bg-transparent bg-none px-4 py-2.5 pr-11 pl-4 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                          onclick="this.showPicker()"
                        />
                        <span
                          class="pointer-events-none absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400"
                        >
                          <svg
                            class="fill-current"
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M6.66659 1.5415C7.0808 1.5415 7.41658 1.87729 7.41658 2.2915V2.99984H12.5833V2.2915C12.5833 1.87729 12.919 1.5415 13.3333 1.5415C13.7475 1.5415 14.0833 1.87729 14.0833 2.2915V2.99984L15.4166 2.99984C16.5212 2.99984 17.4166 3.89527 17.4166 4.99984V7.49984V15.8332C17.4166 16.9377 16.5212 17.8332 15.4166 17.8332H4.58325C3.47868 17.8332 2.58325 16.9377 2.58325 15.8332V7.49984V4.99984C2.58325 3.89527 3.47868 2.99984 4.58325 2.99984L5.91659 2.99984V2.2915C5.91659 1.87729 6.25237 1.5415 6.66659 1.5415ZM6.66659 4.49984H4.58325C4.30711 4.49984 4.08325 4.7237 4.08325 4.99984V6.74984H15.9166V4.99984C15.9166 4.7237 15.6927 4.49984 15.4166 4.49984H13.3333H6.66659ZM15.9166 8.24984H4.08325V15.8332C4.08325 16.1093 4.30711 16.3332 4.58325 16.3332H15.4166C15.6927 16.3332 15.9166 16.1093 15.9166 15.8332V8.24984Z"
                              fill=""
                            />
                          </svg>
                        </span>
                      </div>
                    </div>

                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Time Select Input
                      </label>
                      <div class="relative">
                        <input
                          type="time"
                          placeholder="12:00 AM"
                          onclick="this.showPicker()"
                          class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full appearance-none rounded-lg border border-gray-300 bg-transparent bg-none px-4 py-2.5 pr-11 pl-4 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                        />
                        <span
                          class="absolute top-1/2 right-3 -translate-y-1/2 text-gray-500 dark:text-gray-400"
                        >
                          <svg
                            class="fill-current"
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M3.04175 9.99984C3.04175 6.15686 6.1571 3.0415 10.0001 3.0415C13.8431 3.0415 16.9584 6.15686 16.9584 9.99984C16.9584 13.8428 13.8431 16.9582 10.0001 16.9582C6.1571 16.9582 3.04175 13.8428 3.04175 9.99984ZM10.0001 1.5415C5.32867 1.5415 1.54175 5.32843 1.54175 9.99984C1.54175 14.6712 5.32867 18.4582 10.0001 18.4582C14.6715 18.4582 18.4584 14.6712 18.4584 9.99984C18.4584 5.32843 14.6715 1.5415 10.0001 1.5415ZM9.99998 10.7498C9.58577 10.7498 9.24998 10.4141 9.24998 9.99984V5.4165C9.24998 5.00229 9.58577 4.6665 9.99998 4.6665C10.4142 4.6665 10.75 5.00229 10.75 5.4165V9.24984H13.3334C13.7476 9.24984 14.0834 9.58562 14.0834 9.99984C14.0834 10.4141 13.7476 10.7498 13.3334 10.7498H10.0001H9.99998Z"
                              fill=""
                            />
                          </svg>
                        </span>
                      </div>
                    </div>

                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Input with Payment
                      </label>

                      <div class="relative">
                        <input
                          type="text"
                          placeholder="Card number"
                          class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full appearance-none rounded-lg border border-gray-300 bg-transparent bg-none px-4 py-2.5 pl-[62px] text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                        />
                        <span
                          class="absolute top-1/2 left-0 flex h-11 w-[46px] -translate-y-1/2 items-center justify-center border-r border-gray-200 dark:border-gray-800"
                        >
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="6.25"
                              cy="10"
                              r="5.625"
                              fill="#E80B26"
                            />
                            <circle
                              cx="13.75"
                              cy="10"
                              r="5.625"
                              fill="#F59D31"
                            />
                            <path
                              d="M10 14.1924C11.1508 13.1625 11.875 11.6657 11.875 9.99979C11.875 8.33383 11.1508 6.8371 10 5.80713C8.84918 6.8371 8.125 8.33383 8.125 9.99979C8.125 11.6657 8.84918 13.1625 10 14.1924Z"
                              fill="#FC6020"
                            />
                          </svg>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  class="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]"
                >
                  <div class="px-5 py-4 sm:px-6 sm:py-5">
                    <h3
                      class="text-base font-medium text-gray-800 dark:text-white/90"
                    >
                      Select Inputs
                    </h3>
                  </div>
                  <div
                    class="space-y-6 border-t border-gray-100 p-5 sm:p-6 dark:border-gray-800"
                  >
                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Select Input
                      </label>
                      <div
                        x-data="{ isOptionSelected: false }"
                        class="relative z-20 bg-transparent"
                      >
                        <select
                          class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full appearance-none rounded-lg border border-gray-300 bg-transparent bg-none px-4 py-2.5 pr-11 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                          :class="isOptionSelected && 'text-gray-800 dark:text-white/90'"
                          @change="isOptionSelected = true"
                        >
                          <option
                            value=""
                            class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                          >
                            Select Option
                          </option>
                          <option
                            value=""
                            class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                          >
                            Marketing
                          </option>
                          <option
                            value=""
                            class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                          >
                            Template
                          </option>
                          <option
                            value=""
                            class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                          >
                            Development
                          </option>
                        </select>
                        <span
                          class="pointer-events-none absolute top-1/2 right-4 z-30 -translate-y-1/2 text-gray-700 dark:text-gray-400"
                        >
                          <svg
                            class="stroke-current"
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M4.79175 7.396L10.0001 12.6043L15.2084 7.396"
                              stroke=""
                              stroke-width="1.5"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                        </span>
                      </div>
                    </div>

                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Multiple Select Options
                      </label>
                      <div>
                        <select class="hidden" x-cloak id="select">
                          <option value="1">Option 2</option>
                          <option value="2">Option 3</option>
                          <option value="3">Option 4</option>
                          <option value="4">Option 5</option>
                        </select>

                        <div
                          x-data="dropdown()"
                          x-init="loadOptions()"
                          class="flex flex-col items-center"
                        >
                          <input
                            name="values"
                            type="hidden"
                            :value="selectedValues()"
                          />
                          <div class="relative z-20 inline-block w-full">
                            <div class="relative flex flex-col items-center">
                              <div @click="open" class="w-full">
                                <div
                                  class="shadow-theme-xs focus:border-brand-300 focus:shadow-focus-ring dark:focus:border-brand-300 mb-2 flex h-11 rounded-lg border border-gray-300 py-1.5 pr-3 pl-3 outline-hidden transition dark:border-gray-700 dark:bg-gray-900"
                                >
                                  <div class="flex flex-auto flex-wrap gap-2">
                                    <template
                                      x-for="(option,index) in selected"
                                      :key="index"
                                    >
                                      <div
                                        class="group flex items-center justify-center rounded-full border-[0.7px] border-transparent bg-gray-100 py-1 pr-2 pl-2.5 text-sm text-gray-800 hover:border-gray-200 dark:bg-gray-800 dark:text-white/90 dark:hover:border-gray-800"
                                      >
                                        <div
                                          class="max-w-full flex-initial"
                                          x-model="options[option]"
                                          x-text="options[option].text"
                                        ></div>
                                        <div
                                          class="flex flex-auto flex-row-reverse"
                                        >
                                          <div
                                            @click="remove(index,option)"
                                            class="cursor-pointer pl-2 text-gray-500 group-hover:text-gray-400 dark:text-gray-400"
                                          >
                                            <svg
                                              class="fill-current"
                                              role="button"
                                              width="14"
                                              height="14"
                                              viewBox="0 0 14 14"
                                              fill="none"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path
                                                fill-rule="evenodd"
                                                clip-rule="evenodd"
                                                d="M3.40717 4.46881C3.11428 4.17591 3.11428 3.70104 3.40717 3.40815C3.70006 3.11525 4.17494 3.11525 4.46783 3.40815L6.99943 5.93975L9.53095 3.40822C9.82385 3.11533 10.2987 3.11533 10.5916 3.40822C10.8845 3.70112 10.8845 4.17599 10.5916 4.46888L8.06009 7.00041L10.5916 9.53193C10.8845 9.82482 10.8845 10.2997 10.5916 10.5926C10.2987 10.8855 9.82385 10.8855 9.53095 10.5926L6.99943 8.06107L4.46783 10.5927C4.17494 10.8856 3.70006 10.8856 3.40717 10.5927C3.11428 10.2998 3.11428 9.8249 3.40717 9.53201L5.93877 7.00041L3.40717 4.46881Z"
                                                fill=""
                                              />
                                            </svg>
                                          </div>
                                        </div>
                                      </div>
                                    </template>
                                    <div
                                      x-show="selected.length == 0"
                                      class="flex-1"
                                    >
                                      <input
                                        placeholder="Select option"
                                        class="h-full w-full appearance-none border-0 bg-transparent p-1 pr-2 text-sm outline-hidden placeholder:text-gray-800 focus:border-0 focus:ring-0 focus:outline-hidden dark:placeholder:text-white/90"
                                        :value="selectedValues()"
                                      />
                                    </div>
                                  </div>
                                  <div
                                    class="flex w-7 items-center py-1 pr-1 pl-1"
                                  >
                                    <button
                                      type="button"
                                      @click="open"
                                      class="h-5 w-5 cursor-pointer text-gray-700 outline-hidden focus:outline-hidden dark:text-gray-400"
                                      :class="isOpen() === true ? 'rotate-180' : ''"
                                    >
                                      <svg
                                        class="stroke-current"
                                        width="20"
                                        height="20"
                                        viewBox="0 0 20 20"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                      >
                                        <path
                                          d="M4.79175 7.39551L10.0001 12.6038L15.2084 7.39551"
                                          stroke=""
                                          stroke-width="1.5"
                                          stroke-linecap="round"
                                          stroke-linejoin="round"
                                        />
                                      </svg>
                                    </button>
                                  </div>
                                </div>
                              </div>
                              <div class="w-full px-4">
                                <div
                                  x-show.transition.origin.top="isOpen()"
                                  class="max-h-select absolute top-full left-0 z-40 w-full overflow-y-auto rounded-lg bg-white shadow-sm dark:bg-gray-900"
                                  @click.outside="close"
                                >
                                  <div class="flex w-full flex-col">
                                    <template
                                      x-for="(option,index) in options"
                                      :key="index"
                                    >
                                      <div>
                                        <div
                                          class="hover:bg-primary/5 w-full cursor-pointer rounded-t border-b border-gray-200 dark:border-gray-800"
                                          @click="select(index,$event)"
                                        >
                                          <div
                                            :class="option.selected ? 'border-primary' : ''"
                                            class="relative flex w-full items-center border-l-2 border-transparent p-2 pl-2"
                                          >
                                            <div
                                              class="flex w-full items-center"
                                            >
                                              <div
                                                class="mx-2 leading-6 text-gray-800 dark:text-white/90"
                                                x-model="option"
                                                x-text="option.text"
                                              ></div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </template>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  class="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]"
                >
                  <div class="px-5 py-4 sm:px-6 sm:py-5">
                    <h3
                      class="text-base font-medium text-gray-800 dark:text-white/90"
                    >
                      Textarea input field
                    </h3>
                  </div>
                  <div
                    class="space-y-6 border-t border-gray-100 p-5 sm:p-6 dark:border-gray-800"
                  >
                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Description
                      </label>
                      <textarea
                        placeholder="Enter a description..."
                        type="text"
                        rows="6"
                        class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                      ></textarea>
                    </div>

                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-300 dark:text-white/15"
                      >
                        Description
                      </label>
                      <textarea
                        placeholder="Enter a description..."
                        type="text"
                        rows="6"
                        disabled
                        class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:shadow-focus-ring dark:focus:border-brand-800 w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-0 focus:outline-hidden disabled:border-gray-100 disabled:bg-gray-50 disabled:placeholder:text-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:disabled:border-gray-800 dark:disabled:bg-white/[0.03] dark:disabled:placeholder:text-white/15"
                      ></textarea>
                    </div>

                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Description
                      </label>
                      <textarea
                        placeholder="Enter a description..."
                        type="text"
                        rows="6"
                        class="dark:bg-dark-900 border-error-300 shadow-theme-xs focus:border-error-300 focus:ring-error-500/10 dark:border-error-700 dark:focus:border-error-800 w-full rounded-lg border bg-transparent px-4 py-2.5 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                      ></textarea>
                      <p class="text-theme-xs text-error-500">
                        Please enter a message in the textarea.
                      </p>
                    </div>
                  </div>
                </div>

                <div
                  class="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]"
                >
                  <div class="px-5 py-4 sm:px-6 sm:py-5">
                    <h3
                      class="text-base font-medium text-gray-800 dark:text-white/90"
                    >
                      Input States
                    </h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Validation styles for error, success and disabled states
                      on form controls.
                    </p>
                  </div>
                  <div
                    class="space-y-6 border-t border-gray-100 p-5 sm:p-6 dark:border-gray-800"
                  >
                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Email
                      </label>
                      <div class="relative">
                        <input
                          type="text"
                          value="demoemail"
                          class="dark:bg-dark-900 border-error-300 shadow-theme-xs focus:border-error-300 focus:ring-error-500/10 dark:border-error-700 dark:focus:border-error-800 w-full rounded-lg border bg-transparent px-4 py-2.5 pr-10 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                        />
                        <span
                          class="absolute top-1/2 right-3.5 -translate-y-1/2"
                        >
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M2.58325 7.99967C2.58325 5.00813 5.00838 2.58301 7.99992 2.58301C10.9915 2.58301 13.4166 5.00813 13.4166 7.99967C13.4166 10.9912 10.9915 13.4163 7.99992 13.4163C5.00838 13.4163 2.58325 10.9912 2.58325 7.99967ZM7.99992 1.08301C4.17995 1.08301 1.08325 4.17971 1.08325 7.99967C1.08325 11.8196 4.17995 14.9163 7.99992 14.9163C11.8199 14.9163 14.9166 11.8196 14.9166 7.99967C14.9166 4.17971 11.8199 1.08301 7.99992 1.08301ZM7.09932 5.01639C7.09932 5.51345 7.50227 5.91639 7.99932 5.91639H7.99999C8.49705 5.91639 8.89999 5.51345 8.89999 5.01639C8.89999 4.51933 8.49705 4.11639 7.99999 4.11639H7.99932C7.50227 4.11639 7.09932 4.51933 7.09932 5.01639ZM7.99998 11.8306C7.58576 11.8306 7.24998 11.4948 7.24998 11.0806V7.29627C7.24998 6.88206 7.58576 6.54627 7.99998 6.54627C8.41419 6.54627 8.74998 6.88206 8.74998 7.29627V11.0806C8.74998 11.4948 8.41419 11.8306 7.99998 11.8306Z"
                              fill="#F04438"
                            />
                          </svg>
                        </span>
                      </div>

                      <p class="text-theme-xs text-error-500 mt-1.5">
                        This is an error message.
                      </p>
                    </div>

                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Email
                      </label>
                      <div class="relative">
                        <input
                          type="text"
                          value="<EMAIL>"
                          class="dark:bg-dark-900 border-success-300 shadow-theme-xs focus:border-success-300 focus:ring-success-500/10 dark:border-success-700 dark:focus:border-success-800 w-full rounded-lg border bg-transparent px-4 py-2.5 pr-10 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                        />
                        <span
                          class="absolute top-1/2 right-3.5 -translate-y-1/2"
                        >
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 16 16"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M2.61792 8.00034C2.61792 5.02784 5.0276 2.61816 8.00009 2.61816C10.9726 2.61816 13.3823 5.02784 13.3823 8.00034C13.3823 10.9728 10.9726 13.3825 8.00009 13.3825C5.0276 13.3825 2.61792 10.9728 2.61792 8.00034ZM8.00009 1.11816C4.19917 1.11816 1.11792 4.19942 1.11792 8.00034C1.11792 11.8013 4.19917 14.8825 8.00009 14.8825C11.801 14.8825 14.8823 11.8013 14.8823 8.00034C14.8823 4.19942 11.801 1.11816 8.00009 1.11816ZM10.5192 7.266C10.8121 6.97311 10.8121 6.49823 10.5192 6.20534C10.2264 5.91245 9.75148 5.91245 9.45858 6.20534L7.45958 8.20434L6.54162 7.28638C6.24873 6.99349 5.77385 6.99349 5.48096 7.28638C5.18807 7.57927 5.18807 8.05415 5.48096 8.34704L6.92925 9.79533C7.0699 9.93599 7.26067 10.015 7.45958 10.015C7.6585 10.015 7.84926 9.93599 7.98991 9.79533L10.5192 7.266Z"
                              fill="#12B76A"
                            />
                          </svg>
                        </span>
                      </div>

                      <p class="text-theme-xs text-success-500 mt-1.5">
                        This is an success message.
                      </p>
                    </div>

                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-300 dark:text-white/15"
                      >
                        Email
                      </label>
                      <input
                        type="text"
                        placeholder="<EMAIL>"
                        disabled
                        class="shadow-theme-xs focus:border-brand-300 focus:shadow-focus-ring dark:focus:border-brand-300 h-11 w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 text-sm text-gray-800 placeholder:text-gray-400 focus:outline-hidden disabled:border-gray-100 disabled:placeholder:text-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-gray-400 dark:disabled:border-gray-800 dark:disabled:placeholder:text-white/15"
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div class="space-y-6">
                <div
                  class="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]"
                >
                  <div class="px-5 py-4 sm:px-6 sm:py-5">
                    <h3
                      class="text-base font-medium text-gray-800 dark:text-white/90"
                    >
                      Input Group
                    </h3>
                  </div>
                  <div
                    class="space-y-6 border-t border-gray-100 p-5 sm:p-6 dark:border-gray-800"
                  >
                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Email
                      </label>
                      <div class="relative">
                        <span
                          class="absolute top-1/2 left-0 -translate-y-1/2 border-r border-gray-200 px-3.5 py-3 text-gray-500 dark:border-gray-800 dark:text-gray-400"
                        >
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M3.04175 7.06206V14.375C3.04175 14.6511 3.26561 14.875 3.54175 14.875H16.4584C16.7346 14.875 16.9584 14.6511 16.9584 14.375V7.06245L11.1443 11.1168C10.457 11.5961 9.54373 11.5961 8.85638 11.1168L3.04175 7.06206ZM16.9584 5.19262C16.9584 5.19341 16.9584 5.1942 16.9584 5.19498V5.20026C16.9572 5.22216 16.946 5.24239 16.9279 5.25501L10.2864 9.88638C10.1145 10.0062 9.8862 10.0062 9.71437 9.88638L3.07255 5.25485C3.05342 5.24151 3.04202 5.21967 3.04202 5.19636C3.042 5.15695 3.07394 5.125 3.11335 5.125H16.8871C16.9253 5.125 16.9564 5.15494 16.9584 5.19262ZM18.4584 5.21428V14.375C18.4584 15.4796 17.563 16.375 16.4584 16.375H3.54175C2.43718 16.375 1.54175 15.4796 1.54175 14.375V5.19498C1.54175 5.1852 1.54194 5.17546 1.54231 5.16577C1.55858 4.31209 2.25571 3.625 3.11335 3.625H16.8871C17.7549 3.625 18.4584 4.32843 18.4585 5.19622C18.4585 5.20225 18.4585 5.20826 18.4584 5.21428Z"
                              fill="#667085"
                            />
                          </svg>
                        </span>
                        <input
                          type="text"
                          placeholder="<EMAIL>"
                          class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 pl-[62px] text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                        />
                      </div>
                    </div>

                    <!-- Elements -->
                    <div
                      x-data="{
                          selectedCountry: 'US',
                          countryCodes: {
                              'US': '+1',
                              'GB': '+44',
                              'CA': '+1',
                              'AU': '+61'
                          },
                          phoneNumber: ''
                      }"
                    >
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Phone
                      </label>
                      <div class="relative">
                        <div class="absolute">
                          <select
                            x-model="selectedCountry"
                            @change="phoneNumber = countryCodes[selectedCountry]"
                            class="focus:border-brand-300 focus:ring-brand-500/10 appearance-none rounded-l-lg border-0 border-r border-gray-200 bg-transparent bg-none py-3 pr-8 pl-3.5 leading-tight text-gray-700 focus:ring-3 focus:outline-hidden dark:border-gray-800 dark:text-gray-400"
                          >
                            <option
                              value="US"
                              class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                            >
                              US
                            </option>
                            <option
                              value="GB"
                              class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                            >
                              GB
                            </option>
                            <option
                              value="CA"
                              class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                            >
                              CA
                            </option>
                            <option
                              value="AU"
                              class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                            >
                              AU
                            </option>
                            <!-- Add more country codes as needed -->
                          </select>
                          <div
                            class="pointer-events-none absolute inset-y-0 right-3 flex items-center text-gray-700 dark:text-gray-400"
                          >
                            <svg
                              class="stroke-current"
                              width="20"
                              height="20"
                              viewBox="0 0 20 20"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M4.79175 7.396L10.0001 12.6043L15.2084 7.396"
                                stroke=""
                                stroke-width="1.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                            </svg>
                          </div>
                        </div>
                        <input
                          placeholder="+****************"
                          x-model="phoneNumber"
                          type="tel"
                          class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full rounded-lg border border-gray-300 bg-transparent py-3 pr-4 pl-[84px] text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                        />
                      </div>
                    </div>

                    <!-- Elements -->
                    <div
                      x-data="{
                          selectedCountry: 'US',
                          countryCodes: {
                              'US': '+1',
                              'GB': '+44',
                              'CA': '+1',
                              'AU': '+61'
                          },
                          phoneNumber: ''
                      }"
                    >
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Phone
                      </label>
                      <div class="relative">
                        <div class="absolute right-0">
                          <select
                            x-model="selectedCountry"
                            @change="phoneNumber = countryCodes[selectedCountry]"
                            class="focus:border-brand-300 focus:ring-brand-500/10 appearance-none rounded-r-lg border-0 border-l border-gray-200 bg-transparent bg-none py-3 pr-8 pl-3.5 leading-tight text-gray-700 focus:ring-3 focus:outline-hidden dark:border-gray-800 dark:text-gray-400"
                          >
                            <option
                              value="US"
                              class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                            >
                              US
                            </option>
                            <option
                              value="GB"
                              class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                            >
                              GB
                            </option>
                            <option
                              value="CA"
                              class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                            >
                              CA
                            </option>
                            <option
                              value="AU"
                              class="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
                            >
                              AU
                            </option>
                            <!-- Add more country codes as needed -->
                          </select>
                          <div
                            class="pointer-events-none absolute inset-y-0 right-3 flex items-center text-gray-700 dark:text-gray-400"
                          >
                            <svg
                              class="stroke-current"
                              width="20"
                              height="20"
                              viewBox="0 0 20 20"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M4.79175 7.396L10.0001 12.6043L15.2084 7.396"
                                stroke=""
                                stroke-width="1.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                            </svg>
                          </div>
                        </div>
                        <input
                          placeholder="+****************"
                          x-model="phoneNumber"
                          type="tel"
                          class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full rounded-lg border border-gray-300 bg-transparent py-3 pr-[84px] pl-3 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                        />
                      </div>
                    </div>

                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        URL
                      </label>
                      <div class="relative">
                        <span
                          class="absolute top-1/2 left-0 inline-flex h-11 -translate-y-1/2 items-center justify-center border-r border-gray-200 py-3 pr-3 pl-3.5 text-gray-500 dark:border-gray-800 dark:text-gray-400"
                        >
                          http://
                        </span>
                        <input
                          type="url"
                          placeholder="www.tailadmin.com"
                          class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full rounded-lg border border-gray-300 bg-transparent px-4 py-2.5 pl-[90px] text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                        />
                      </div>
                    </div>

                    <!-- Elements -->
                    <div id="copy-input">
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Website
                      </label>
                      <div class="relative">
                        <button
                          id="copy-button"
                          class="absolute top-1/2 right-0 inline-flex -translate-y-1/2 cursor-pointer items-center gap-1 border-l border-gray-200 py-3 pr-3 pl-3.5 text-sm font-medium text-gray-700 dark:border-gray-800 dark:text-gray-400"
                        >
                          <svg
                            class="fill-current"
                            width="20"
                            height="20"
                            viewBox="0 0 20 20"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M6.58822 4.58398C6.58822 4.30784 6.81207 4.08398 7.08822 4.08398H15.4154C15.6915 4.08398 15.9154 4.30784 15.9154 4.58398L15.9154 12.9128C15.9154 13.189 15.6916 13.4128 15.4154 13.4128H7.08821C6.81207 13.4128 6.58822 13.189 6.58822 12.9128V4.58398ZM7.08822 2.58398C5.98365 2.58398 5.08822 3.47942 5.08822 4.58398V5.09416H4.58496C3.48039 5.09416 2.58496 5.98959 2.58496 7.09416V15.4161C2.58496 16.5207 3.48039 17.4161 4.58496 17.4161H12.9069C14.0115 17.4161 14.9069 16.5207 14.9069 15.4161L14.9069 14.9128H15.4154C16.52 14.9128 17.4154 14.0174 17.4154 12.9128L17.4154 4.58398C17.4154 3.47941 16.52 2.58398 15.4154 2.58398H7.08822ZM13.4069 14.9128H7.08821C5.98364 14.9128 5.08822 14.0174 5.08822 12.9128V6.59416H4.58496C4.30882 6.59416 4.08496 6.81801 4.08496 7.09416V15.4161C4.08496 15.6922 4.30882 15.9161 4.58496 15.9161H12.9069C13.183 15.9161 13.4069 15.6922 13.4069 15.4161L13.4069 14.9128Z"
                              fill=""
                            />
                          </svg>
                          <div id="copy-text">Copy</div>
                        </button>
                        <input
                          value="www.tailadmin.com"
                          type="url"
                          id="website-input"
                          class="dark:bg-dark-900 shadow-theme-xs focus:border-brand-300 focus:ring-brand-500/10 dark:focus:border-brand-800 h-11 w-full rounded-lg border border-gray-300 bg-transparent py-3 pr-[90px] pl-4 text-sm text-gray-800 placeholder:text-gray-400 focus:ring-3 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  class="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]"
                >
                  <div class="px-5 py-4 sm:px-6 sm:py-5">
                    <h3
                      class="text-base font-medium text-gray-800 dark:text-white/90"
                    >
                      File Input
                    </h3>
                  </div>
                  <div
                    class="space-y-6 border-t border-gray-100 p-5 sm:p-6 dark:border-gray-800"
                  >
                    <!-- Elements -->
                    <div>
                      <label
                        class="mb-1.5 block text-sm font-medium text-gray-700 dark:text-gray-400"
                      >
                        Upload file
                      </label>
                      <input
                        type="file"
                        class="focus:border-ring-brand-300 shadow-theme-xs focus:file:ring-brand-300 h-11 w-full overflow-hidden rounded-lg border border-gray-300 bg-transparent text-sm text-gray-500 transition-colors file:mr-5 file:border-collapse file:cursor-pointer file:rounded-l-lg file:border-0 file:border-r file:border-solid file:border-gray-200 file:bg-gray-50 file:py-3 file:pr-3 file:pl-3.5 file:text-sm file:text-gray-700 placeholder:text-gray-400 hover:file:bg-gray-100 focus:outline-hidden dark:border-gray-700 dark:bg-gray-900 dark:text-gray-400 dark:text-white/90 dark:file:border-gray-800 dark:file:bg-white/[0.03] dark:file:text-gray-400 dark:placeholder:text-gray-400"
                      />
                    </div>
                  </div>
                </div>

                <div
                  class="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]"
                >
                  <div class="px-5 py-4 sm:px-6 sm:py-5">
                    <h3
                      class="text-base font-medium text-gray-800 dark:text-white/90"
                    >
                      Checkboxes
                    </h3>
                  </div>
                  <div
                    class="space-y-6 border-t border-gray-100 p-5 sm:p-6 dark:border-gray-800"
                  >
                    <!-- Elements -->
                    <div class="flex flex-wrap items-center gap-8">
                      <div x-data="{ checkboxToggle: false }">
                        <label
                          for="checkboxLabelOne"
                          class="flex cursor-pointer items-center text-sm font-medium text-gray-700 select-none dark:text-gray-400"
                        >
                          <div class="relative">
                            <input
                              type="checkbox"
                              id="checkboxLabelOne"
                              class="sr-only"
                              @change="checkboxToggle = !checkboxToggle"
                            />
                            <div
                              :class="checkboxToggle ? 'border-brand-500 bg-brand-500' : 'bg-transparent border-gray-300 dark:border-gray-700'"
                              class="f hover:border-brand-500 dark:hover:border-brand-500 mr-3 flex h-5 w-5 items-center justify-center rounded-md border-[1.25px]"
                            >
                              <span :class="checkboxToggle ? '' : 'opacity-0'">
                                <svg
                                  width="14"
                                  height="14"
                                  viewBox="0 0 14 14"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M11.6666 3.5L5.24992 9.91667L2.33325 7"
                                    stroke="white"
                                    stroke-width="1.94437"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                  />
                                </svg>
                              </span>
                            </div>
                          </div>
                          Default
                        </label>
                      </div>

                      <div x-data="{ checkboxToggle: true }">
                        <label
                          for="checkboxLabelTwo"
                          class="flex cursor-pointer items-center text-sm font-medium text-gray-700 select-none dark:text-gray-400"
                        >
                          <div class="relative">
                            <input
                              type="checkbox"
                              id="checkboxLabelTwo"
                              class="sr-only"
                              @change="checkboxToggle = !checkboxToggle"
                            />
                            <div
                              :class="checkboxToggle ? 'border-brand-500 bg-brand-500' : 'bg-transparent border-gray-300 dark:border-gray-700'"
                              class="hover:border-brand-500 dark:hover:border-brand-500 mr-3 flex h-5 w-5 items-center justify-center rounded-md border-[1.25px]"
                            >
                              <span :class="checkboxToggle ? '' : 'opacity-0'">
                                <svg
                                  width="14"
                                  height="14"
                                  viewBox="0 0 14 14"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M11.6666 3.5L5.24992 9.91667L2.33325 7"
                                    stroke="white"
                                    stroke-width="1.94437"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                  />
                                </svg>
                              </span>
                            </div>
                          </div>
                          Checked
                        </label>
                      </div>

                      <div x-data="{ checkboxToggle: true }">
                        <label
                          for="checkboxLabelThree"
                          class="flex cursor-pointer items-center text-sm font-medium text-gray-300 select-none dark:text-gray-700"
                        >
                          <div class="relative">
                            <input
                              type="checkbox"
                              id="checkboxLabelThree"
                              class="peer sr-only"
                              @change="checkboxToggle = !checkboxToggle"
                              disabled
                            />
                            <div
                              :class="checkboxToggle ? 'bg-transparent border-gray-200 dark:border-gray-800' : 'border-brand-500 bg-brand-500'"
                              class="mr-3 flex h-5 w-5 items-center justify-center rounded-md border-[1.25px]"
                            >
                              <span :class="checkboxToggle ? '' : 'opacity-0'">
                                <svg
                                  class="stroke-gray-200 dark:stroke-gray-800"
                                  width="14"
                                  height="14"
                                  viewBox="0 0 14 14"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M11.6666 3.5L5.24992 9.91667L2.33325 7"
                                    stroke=""
                                    stroke-width="2.33333"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                  />
                                </svg>
                              </span>
                            </div>
                          </div>
                          Disabled
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  class="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]"
                >
                  <div class="px-5 py-4 sm:px-6 sm:py-5">
                    <h3
                      class="text-base font-medium text-gray-800 dark:text-white/90"
                    >
                      Radio Buttons
                    </h3>
                  </div>
                  <div
                    class="space-y-6 border-t border-gray-100 p-5 sm:p-6 dark:border-gray-800"
                  >
                    <!-- Elements -->
                    <div class="flex flex-wrap items-center gap-8">
                      <div x-data="{ checkboxToggle: false }">
                        <label
                          for="radioLabelOne"
                          class="flex cursor-pointer items-center text-sm font-medium text-gray-700 select-none dark:text-gray-400"
                        >
                          <div class="relative">
                            <input
                              type="checkbox"
                              id="radioLabelOne"
                              class="sr-only"
                              @change="checkboxToggle = !checkboxToggle"
                            />
                            <div
                              :class="checkboxToggle ? 'border-brand-500 bg-brand-500' : 'bg-transparent border-gray-300 dark:border-gray-700'"
                              class="hover:border-brand-500 dark:hover:border-brand-500 mr-3 flex h-5 w-5 items-center justify-center rounded-full border-[1.25px]"
                            >
                              <span
                                class="h-2 w-2 rounded-full"
                                :class="checkboxToggle ? 'bg-white' : 'bg-white dark:bg-[#171f2e]'"
                              ></span>
                            </div>
                          </div>
                          Default
                        </label>
                      </div>

                      <div x-data="{ checkboxToggle: true }">
                        <label
                          for="radioLabelTwo"
                          class="flex cursor-pointer items-center text-sm font-medium text-gray-700 select-none dark:text-gray-400"
                        >
                          <div class="relative">
                            <input
                              type="checkbox"
                              id="radioLabelTwo"
                              class="sr-only"
                              @change="checkboxToggle = !checkboxToggle"
                            />
                            <div
                              :class="checkboxToggle ? 'border-brand-500 bg-brand-500' : 'bg-transparent border-gray-300 dark:border-gray-700'"
                              class="hover:border-brand-500 dark:hover:border-brand-500 mr-3 flex h-5 w-5 items-center justify-center rounded-full border-[1.25px]"
                            >
                              <span
                                class="h-2 w-2 rounded-full"
                                :class="checkboxToggle ? 'bg-white' : 'bg-white dark:bg-[#171f2e]'"
                              ></span>
                            </div>
                          </div>
                          Secondary
                        </label>
                      </div>

                      <div x-data="{ checkboxToggle: false }">
                        <label
                          for="radioLabelThree"
                          class="flex cursor-pointer items-center text-sm font-medium text-gray-300 select-none dark:text-gray-700"
                        >
                          <div class="relative">
                            <input
                              type="checkbox"
                              id="radioLabelThree"
                              class="peer sr-only"
                              @change="checkboxToggle = !checkboxToggle"
                              disabled
                            />
                            <div
                              :class="checkboxToggle ? 'bg-transparent border-gray-300 dark:border-gray-700' : 'border-brand-500 bg-brand-500'"
                              class="mr-3 flex h-5 w-5 items-center justify-center rounded-full border-[1.25px]"
                            >
                              <span
                                class="h-2 w-2 rounded-full"
                                :class="checkboxToggle ? 'bg-white' : 'bg-white dark:bg-[#171f2e]'"
                              ></span>
                            </div>
                          </div>
                          Disabled Secondary
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  class="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]"
                >
                  <div class="px-5 py-4 sm:px-6 sm:py-5">
                    <h3
                      class="text-base font-medium text-gray-800 dark:text-white/90"
                    >
                      Toggle switch input
                    </h3>
                  </div>
                  <div
                    class="space-y-6 border-t border-gray-100 p-5 sm:p-6 dark:border-gray-800"
                  >
                    <!-- Elements -->
                    <div
                      class="mb-6 flex flex-wrap items-center gap-6 sm:gap-8"
                    >
                      <div x-data="{ switcherToggle: false }">
                        <label
                          for="toggle1"
                          class="flex cursor-pointer items-center gap-3 text-sm font-medium text-gray-700 select-none dark:text-gray-400"
                        >
                          <div class="relative">
                            <input
                              type="checkbox"
                              id="toggle1"
                              class="sr-only"
                              @change="switcherToggle = !switcherToggle"
                            />
                            <div
                              class="block h-6 w-11 rounded-full"
                              :class="switcherToggle ? 'bg-brand-500 dark:bg-brand-500' : 'bg-gray-200 dark:bg-white/10'"
                            ></div>
                            <div
                              :class="switcherToggle ? 'translate-x-full': 'translate-x-0'"
                              class="shadow-theme-sm absolute top-0.5 left-0.5 h-5 w-5 rounded-full bg-white duration-300 ease-linear"
                            ></div>
                          </div>

                          Default
                        </label>
                      </div>

                      <div x-data="{ switcherToggle: true }">
                        <label
                          for="toggle2"
                          class="flex cursor-pointer items-center gap-3 text-sm font-medium text-gray-700 select-none dark:text-gray-400"
                        >
                          <div class="relative">
                            <input
                              type="checkbox"
                              id="toggle2"
                              class="sr-only"
                              @change="switcherToggle = !switcherToggle"
                            />
                            <div
                              class="block h-6 w-11 rounded-full"
                              :class="switcherToggle ? 'bg-brand-500 dark:bg-brand-500' : 'bg-gray-200 dark:bg-white/10'"
                            ></div>
                            <div
                              :class="switcherToggle ? 'translate-x-full': 'translate-x-0'"
                              class="shadow-theme-sm absolute top-0.5 left-0.5 h-5 w-5 rounded-full bg-white duration-300 ease-linear"
                            ></div>
                          </div>

                          Checked
                        </label>
                      </div>

                      <div x-data="{ switcherToggle: false }">
                        <label
                          for="toggle3"
                          class="flex cursor-pointer items-center gap-3 text-sm font-medium text-gray-400 select-none"
                        >
                          <div class="relative">
                            <input
                              type="checkbox"
                              id="toggle3"
                              class="sr-only"
                              @change="switcherToggle = !switcherToggle"
                              disabled
                            />
                            <div
                              class="block h-6 w-11 rounded-full"
                              :class="switcherToggle ? 'bg-brand-500 dark:bg-brand-500' : 'bg-gray-100 dark:bg-gray-800'"
                            ></div>
                            <div
                              :class="switcherToggle ? 'translate-x-full': 'translate-x-0'"
                              class="shadow-theme-sm absolute top-0.5 left-0.5 h-5 w-5 rounded-full bg-gray-50 duration-300 ease-linear"
                            ></div>
                          </div>

                          Disabled
                        </label>
                      </div>
                    </div>

                    <!-- Elements -->
                    <div class="flex flex-wrap items-center gap-6 sm:gap-8">
                      <div x-data="{ switcherToggle: false }">
                        <label
                          for="toggle11"
                          class="flex cursor-pointer items-center gap-3 text-sm font-medium text-gray-700 select-none dark:text-gray-400"
                        >
                          <div class="relative">
                            <input
                              type="checkbox"
                              id="toggle11"
                              class="sr-only"
                              @change="switcherToggle = !switcherToggle"
                            />
                            <div
                              class="block h-6 w-11 rounded-full bg-gray-700 dark:bg-white/10"
                            ></div>
                            <div
                              :class="switcherToggle ? 'translate-x-full': 'translate-x-0'"
                              class="shadow-theme-sm absolute top-0.5 left-0.5 h-5 w-5 rounded-full bg-white duration-300 ease-linear"
                            ></div>
                          </div>

                          Default
                        </label>
                      </div>

                      <div x-data="{ switcherToggle: true }">
                        <label
                          for="toggle22"
                          class="flex cursor-pointer items-center gap-3 text-sm font-medium text-gray-700 select-none dark:text-gray-400"
                        >
                          <div class="relative">
                            <input
                              type="checkbox"
                              id="toggle22"
                              class="sr-only"
                              @change="switcherToggle = !switcherToggle"
                            />
                            <div
                              class="block h-6 w-11 rounded-full bg-gray-700 dark:bg-white/10"
                            ></div>
                            <div
                              :class="switcherToggle ? 'translate-x-full': 'translate-x-0'"
                              class="shadow-theme-sm absolute top-0.5 left-0.5 h-5 w-5 rounded-full bg-white duration-300 ease-linear"
                            ></div>
                          </div>

                          Checked
                        </label>
                      </div>

                      <div x-data="{ switcherToggle: false }">
                        <label
                          for="toggle33"
                          class="flex cursor-pointer items-center gap-3 text-sm font-medium text-gray-400 select-none"
                        >
                          <div class="relative">
                            <input
                              type="checkbox"
                              id="toggle33"
                              class="sr-only"
                              @change="switcherToggle = !switcherToggle"
                              disabled
                            />
                            <div
                              class="block h-6 w-11 rounded-full bg-gray-100 dark:bg-gray-800"
                            ></div>
                            <div
                              :class="switcherToggle ? 'translate-x-full': 'translate-x-0'"
                              class="shadow-theme-sm absolute top-0.5 left-0.5 h-5 w-5 rounded-full bg-gray-50 duration-300 ease-linear"
                            ></div>
                          </div>

                          Disabled
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <div
                  class="rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03]"
                >
                  <div class="px-5 py-4 sm:px-6 sm:py-5">
                    <h3
                      class="text-base font-medium text-gray-800 dark:text-white/90"
                    >
                      Dropzone
                    </h3>
                  </div>
                  <div
                    class="space-y-6 border-t border-gray-100 p-5 sm:p-6 dark:border-gray-800"
                  >
                    <form
                      class="dropzone hover:border-brand-500! dark:hover:border-brand-500! rounded-xl border border-dashed! border-gray-300! bg-gray-50 p-7 lg:p-10 dark:border-gray-700! dark:bg-gray-900"
                      id="demo-upload"
                      action="/upload"
                    >
                      <div class="dz-message m-0!">
                        <div class="mb-[22px] flex justify-center">
                          <div
                            class="flex h-[68px] w-[68px] items-center justify-center rounded-full bg-gray-200 text-gray-700 dark:bg-gray-800 dark:text-gray-400"
                          >
                            <svg
                              class="fill-current"
                              width="29"
                              height="28"
                              viewBox="0 0 29 28"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M14.5019 3.91699C14.2852 3.91699 14.0899 4.00891 13.953 4.15589L8.57363 9.53186C8.28065 9.82466 8.2805 10.2995 8.5733 10.5925C8.8661 10.8855 9.34097 10.8857 9.63396 10.5929L13.7519 6.47752V18.667C13.7519 19.0812 14.0877 19.417 14.5019 19.417C14.9161 19.417 15.2519 19.0812 15.2519 18.667V6.48234L19.3653 10.5929C19.6583 10.8857 20.1332 10.8855 20.426 10.5925C20.7188 10.2995 20.7186 9.82463 20.4256 9.53184L15.0838 4.19378C14.9463 4.02488 14.7367 3.91699 14.5019 3.91699ZM5.91626 18.667C5.91626 18.2528 5.58047 17.917 5.16626 17.917C4.75205 17.917 4.41626 18.2528 4.41626 18.667V21.8337C4.41626 23.0763 5.42362 24.0837 6.66626 24.0837H22.3339C23.5766 24.0837 24.5839 23.0763 24.5839 21.8337V18.667C24.5839 18.2528 24.2482 17.917 23.8339 17.917C23.4197 17.917 23.0839 18.2528 23.0839 18.667V21.8337C23.0839 22.2479 22.7482 22.5837 22.3339 22.5837H6.66626C6.25205 22.5837 5.91626 22.2479 5.91626 21.8337V18.667Z"
                                fill=""
                              />
                            </svg>
                          </div>
                        </div>

                        <h4
                          class="text-theme-xl mb-3 font-semibold text-gray-800 dark:text-white/90"
                        >
                          Drop File Here
                        </h4>
                        <span
                          class="mx-auto mb-5 block w-full max-w-[290px] text-sm text-gray-700 dark:text-gray-400"
                        >
                          Drag and drop your PNG, JPG, WebP, SVG images here or
                          browse
                        </span>

                        <span
                          class="text-theme-sm text-brand-500 font-medium underline"
                        >
                          Browse File
                        </span>
                      </div>
                    </form>
                  </div>
                </div>
              </div>
            </div>
            <!-- ====== Form Elements Section End -->
          </div>
        </main>
        <!-- ===== Main Content End ===== -->
      </div>
      <!-- ===== Content Area End ===== -->
    </div>
    <!-- ===== Page Wrapper End ===== -->

    <script>
      function dropdown() {
        return {
          options: [],
          selected: [],
          show: false,
          open() {
            this.show = true;
          },
          close() {
            this.show = false;
          },
          isOpen() {
            return this.show === true;
          },
          select(index, event) {
            if (!this.options[index].selected) {
              this.options[index].selected = true;
              this.options[index].element = event.target;
              this.selected.push(index);
            } else {
              this.selected.splice(this.selected.lastIndexOf(index), 1);
              this.options[index].selected = false;
            }
          },
          remove(index, option) {
            this.options[option].selected = false;
            this.selected.splice(index, 1);
          },
          loadOptions() {
            const options = document.getElementById("select").options;
            for (let i = 0; i < options.length; i++) {
              this.options.push({
                value: options[i].value,
                text: options[i].innerText,
                selected:
                  options[i].getAttribute("selected") != null
                    ? options[i].getAttribute("selected")
                    : false,
              });
            }
          },
          selectedValues() {
            return this.selected.map((option) => {
              return this.options[option].value;
            });
          },
        };
      }
    </script>
  </body>
</html>
